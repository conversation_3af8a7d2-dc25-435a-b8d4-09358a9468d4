# My LLM Platform

一个功能完整的本地大语言模型平台，支持多种推理后端、认证系统和高级功能。

## 系统概述

My LLM Platform 是一个完整的大语言模型应用平台，提供以下核心功能：

- **多后端支持**：兼容 vLLM、Ollama、X-Inference 等多种推理引擎
- **OpenAI 兼容 API**：提供与 OpenAI API 完全兼容的接口
- **插件系统**：支持 RAG、Agent、MCP 等扩展功能
- **用户认证**：集成 Keycloak 认证系统
- **多语言支持**：界面支持英语、日语和中文
- **现代化前端**：基于 React 的响应式聊天界面

## 系统架构

```
my_llm_platform/
├── backend/              # FastAPI 后端 + LLM 业务逻辑
│   ├── auth/             # 认证相关模块
│   ├── db/               # 数据库模型和管理
│   ├── inference/        # 推理引擎适配器
│   ├── plugins/          # 插件系统
│   └── api/              # API 路由
├── frontend/chat-ui/     # React 聊天界面
│   ├── src/              # 前端源代码
│   │   ├── components/   # UI 组件
│   │   ├── services/     # API 服务
│   │   └── i18n/         # 国际化支持
└── docker-compose.yml    # 服务编排配置
```

## 核心功能

### 1. 多后端推理支持

平台支持多种推理后端，可以根据需要动态切换：

- **vLLM**：高性能推理引擎，适合大规模部署
- **Ollama**：轻量级本地推理，适合个人开发
- **X-Inference**：企业级推理服务
- **自定义后端**：可扩展的后端适配器架构

### 2. 插件系统

平台内置强大的插件系统，支持：

- **RAG (检索增强生成)**：从向量数据库检索相关文档，增强 LLM 回答
- **Agent**：实现 ReAct 模式的智能代理，可以使用工具解决复杂问题
- **MCP (Model Control Protocol)**：标准化的模型控制协议，支持工具调用

### 3. 用户体验

- **流式响应**：实时显示生成内容
- **聊天历史**：保存和管理对话历史
- **多语言支持**：界面支持英语、日语和中文
- **参数调优**：支持创建和管理模型参数配置文件

## 安装指南

### 系统要求

- **Docker / Docker Compose**：用于运行服务容器
- **Python >= 3.10**：后端开发环境
- **Node.js >= 16**：前端开发环境

### Windows 用户推荐环境

强烈建议 Windows 用户使用 **WSL2 + Docker Desktop** 进行开发，以获得更好的性能和兼容性。详细设置请参考[Windows 开发环境设置](#windows-开发环境设置)。

### 快速开始

1. **克隆仓库**

```bash
git clone https://github.com/yourusername/my_llm_platform.git
cd my_llm_platform
```

2. **启动后端服务**
vllm利用的场合
python -m vllm.entrypoints.openai.api_server --model facebook/opt-125m --port 8001 --max-num-seqs 32 --max-num-batched-tokens 512 --max-model-len 512

python3 -m vllm.entrypoints.openai.api_server \
  --model facebook/opt-125m \
  --chat-template '{{ bos_token }}<|user|>{{ message }}<|assistant|>' \
  --chat-template-content-format string \
  --host 0.0.0.0 \
  --port 8001


ollamaの場合
ollama serve

x-inferenceの場合
docker-compose up -d x-inference

```bash
# 启动必要的服务容器qdrant|weaviate
docker-compose up -d redis rabbitmq weaviate

# 安装后端依赖
cd backend
pip install -r requirements.txt

# 启动后端服务
uvicorn backend.main:app --reload
```

3. **启动前端**

```bash
cd frontend/chat-ui
npm install
npm run dev
```

4. **访问应用**

打开浏览器访问 http://localhost:5173

## 使用指南

### 基本使用

1. 在浏览器中打开前端应用
2. 选择要使用的模型和后端
3. 开始与 AI 对话

### 插件使用

在聊天界面中，可以通过设置启用不同的插件：

- **RAG 插件**：自动从知识库检索相关信息
- **Agent 插件**：使用工具解决复杂问题
- **MCP 插件**：支持结构化的工具调用

### API 使用

平台提供与 OpenAI 兼容的 API，可以使用 OpenAI 客户端库直接调用：

```python
from openai import OpenAI

client = OpenAI(
    base_url="http://localhost:8000/v1",
    api_key="dummy-api-key"  # 如果不需要认证
)

response = client.chat.completions.create(
    model="llama3.2",  # 使用的模型名称
    messages=[
        {"role": "user", "content": "你好，请介绍一下自己。"}
    ]
)

print(response.choices[0].message.content)
```

## 开发指南

### 后端开发

- **添加新的推理后端**：在 `backend/inference` 目录中实现新的适配器
- **创建新的插件**：在 `backend/plugins` 目录中添加新的插件实现
- **扩展 API**：在 `backend/api` 目录中添加新的路由

### 前端开发

- **添加新的 UI 组件**：在 `frontend/chat-ui/src/components` 目录中创建
- **添加新的语言支持**：在 `frontend/chat-ui/src/i18n/locales` 目录中添加翻译文件

## 服务管理

### 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 启动特定服务
docker-compose up -d redis qdrant
```

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f redis
```

### 停止服务

```bash
# 停止所有服务
docker-compose down

# 停止并删除所有数据（谨慎使用）
docker-compose down -v
```

## Windows 开发环境设置

### WSL2 安装

1. 在 PowerShell（管理员）中运行：
   ```powershell
   wsl --install
   ```

2. 安装完成后重启计算机

### Docker Desktop 设置

1. 安装 [Docker Desktop](https://www.docker.com/products/docker-desktop/)
2. 在设置中启用 WSL2 集成：
   - 设置 → 「General」→ ✅ "Use the WSL 2 based engine"
   - 设置 → 「Resources > WSL Integration」→ ✅ 启用 Ubuntu

### 在 WSL2 中开发

```bash
# 进入 WSL2
wsl

# 在 WSL2 中克隆和运行项目
cd ~
git clone https://github.com/yourusername/my_llm_platform.git
cd my_llm_platform
```

## 常见问题

### Redis 连接失败

**问题**：无法连接到 Redis 服务
**解决方案**：确保 Redis 容器已启动 `docker-compose up -d redis`

### 模型加载失败

**问题**：无法加载指定的模型
**解决方案**：检查模型名称是否正确，以及后端是否支持该模型

### 前端无法连接后端

**问题**：前端无法连接到后端 API
**解决方案**：检查 API 基础 URL 配置和代理设置

## 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 贡献指南

欢迎贡献代码、报告问题或提出改进建议。请遵循以下步骤：

1. Fork 本仓库
2. 创建您的特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交您的更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开一个 Pull Request
