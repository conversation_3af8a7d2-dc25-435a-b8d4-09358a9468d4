import { create } from "zustand";
import { Message } from "../types/chat";

interface ChatStore {
  messages: Message[];
  loading: boolean;
  error: string | undefined;
  currentStreamingMessage: string;
  currentSessionId: string | null;
  addMessage: (_message: Message) => void;
  setLoading: (_loading: boolean) => void;
  setError: (_error: string | undefined) => void;
  setCurrentStreamingMessage: (_message: string) => void;
  clearMessages: () => void;
  setCurrentSessionId: (_id: string | null) => void;
  resetChat: () => void;
}

export const useChatStore = create<ChatStore>((set) => ({
  messages: [],
  loading: false,
  error: undefined,
  currentStreamingMessage: "",
  currentSessionId: null,
  addMessage: (message) =>
    set((state) => ({
      messages: [...state.messages, message],
    })),
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
  setCurrentStreamingMessage: (message) =>
    set({ currentStreamingMessage: message }),
  clearMessages: () => set({ messages: [] }),
  setCurrentSessionId: (id) => set({ currentSessionId: id }),
  resetChat: () =>
    set({
      messages: [],
      currentSessionId: null,
      currentStreamingMessage: "",
      error: undefined,
    }),
}));
