{"app_name": "My LLM Platform", "loading": "Loading...", "loading_models": "Loading models and backends...", "error": "Error", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "test": "Test", "send": "Send", "settings_label": "Settings", "new_chat": "New Chat", "chat_placeholder": "Type your message here...", "ai_thinking": "AI is thinking...", "empty_messages": "No messages yet. Start a conversation!", "message_failed": "Failed to send message. Please try again.", "welcome_title": "Welcome to My LLM Platform", "welcome_message": "Hello! How can I help you today?", "disclaimer": "Chat responses may not always be accurate. Please verify important information.", "ai_disclaimer": "AI responses may not always be accurate. Please verify important information.", "model": "Model", "select_model": "Select a model", "advanced_options": "Advanced Options", "show_advanced": "Show Advanced", "hide_advanced": "Hide Advanced", "backend": "Backend", "auto_backend": "Auto (Use default)", "tuning_profiles": "Parameter Tuning Profiles", "available_profiles": "Available Profiles", "create_new": "Create New", "profile_name": "Name", "profile_description": "Description", "profile_model": "Model", "profile_backend": "Backend (optional)", "parameters": "Parameters", "parameter_name": "Name", "parameter_value": "Value", "parameter_description": "Description", "add_parameter": "Add Parameter", "remove": "Remove", "test_profile": "Test Profile", "test_prompt_placeholder": "Enter a prompt to test this profile...", "testing": "Testing...", "test_result": "Test Result", "prompt": "Prompt", "output": "Output", "active_profile": "Active Profile", "clear": "Clear", "language": "Language", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System", "model_settings": "Model Settings", "tuning_settings": "Tuning Settings", "interface_settings": "Interface Settings", "about": "About", "version": "Version", "close": "Close", "chat.history": "Chat History", "no_chat_history": "No chat history", "admin_panel": "Admin Panel", "login_required": "Login required. Please log in to view your chat history.", "login": "<PERSON><PERSON>", "username": "Username", "password": "Password", "username_placeholder": "Enter your username", "password_placeholder": "Enter your password", "username_required": "Username is required", "password_required": "Password is required", "login_failed": "<PERSON><PERSON> failed. Please check your credentials and try again.", "logging_in": "Logging in...", "quick_login": "<PERSON>gin", "login_as_user": "<PERSON><PERSON> as User", "login_as_admin": "<PERSON><PERSON> as <PERSON><PERSON>", "chat": {"welcomeTitle": "Welcome to My LLM Platform", "welcomeMessage": "Hello! How can I help you today?", "inputPlaceholder": "Type your message here...", "disclaimer": "AI responses may not always be accurate. Please verify important information.", "errorPrefix": "Error", "errorHelpText": "There was a problem communicating with the server. Please try again later."}, "settings": {"title": "Settings", "model": "Model", "backend": "Backend", "profile": "Profile", "noProfile": "No profile", "clearChat": "Clear Chat"}, "base_url": "Base URL", "optional": "optional", "base_url_help": "Set the base URL for the API, e.g., http://localhost:11434. Leave empty to use the default.", "select_backend": "Select Backend", "response_mode": "Response Mode", "stream_mode": "Streaming Mode", "complete_mode": "Complete Mode", "stream_mode_description": "Responses appear gradually as they are generated.", "complete_mode_description": "Responses appear all at once after generation is complete.", "show_thinking": "Show thinking", "hide_thinking": "Hide thinking", "model_thinking": "Model's thinking process", "plugins": {"title": "Plugins", "loading": "Loading plugins...", "refresh": "Refresh", "retry": "Retry", "noPluginsAvailable": "No plugins available", "selectPlugins": "Select plugins to use", "version": "Version", "author": "Author", "type": "Type", "priority": "Priority", "dependencies": "Dependencies", "tags": "Tags", "configure": "Configure", "configureTitle": "Configure {name}", "configJson": "Configuration (JSON)", "save": "Save", "cancel": "Cancel", "enable": "Enable", "disable": "Disable", "toolsSettings": "<PERSON><PERSON> Settings", "toolsEnabled": "Tools Enabled", "toolsDisabled": "Tools Disabled", "toolsEnabledDescription": "The LLM can use tools to perform actions.", "toolsDisabledDescription": "The LLM cannot use tools to perform actions.", "systemDisabled": "Plugin system is disabled", "enableInstructions": "Please check server settings to enable the plugin system.", "fetchError": "Failed to fetch plugins", "viewDocs": "View documentation"}}