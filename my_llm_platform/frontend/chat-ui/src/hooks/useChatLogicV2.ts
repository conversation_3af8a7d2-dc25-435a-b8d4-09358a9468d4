import { useState, useRef, /* useCallback, */ useEffect } from "react";
// import { useTranslation } from "react-i18next";
import { useChatStore } from "../store/chatStore";
import { Message, ChatOptions /* StreamChunk */ } from "../types/chat";
import { apiService } from "../services/apiService";
import { serviceFactory } from "../services/serviceFactory";

/**
 * チャットロジックフック V2
 * 改良されたアーキテクチャを使用してメッセージの送信、受信、状態管理を処理
 */
export const useChatLogicV2 = () => {
  // 現在は翻訳機能が必要ない
  // const { t } = useTranslation();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const logger = serviceFactory.getLogger();

  // グローバルステートからデータを取得
  const {
    messages: storeMessages,
    addMessage: storeAddMessage,
    currentSessionId: storeCurrentSessionId,
    setCurrentSessionId: storeSetCurrentSessionId,
    clearMessages,
  } = useChatStore();

  // ローカルステート
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // 環境変数からデフォルト値を取得
  const defaultModel = import.meta.env.VITE_DEFAULT_MODEL || "llama3.2:3b";
  const defaultBackend = import.meta.env.VITE_DEFAULT_BACKEND || "ollama";

  // プラグイン関連の状態
  const [selectedPlugins, setSelectedPlugins] = useState<string[]>([]);
  const [useTools, setUseTools] = useState<boolean>(true);

  const [selectedModel, setSelectedModel] = useState<string>(defaultModel);
  const [selectedBackend, setSelectedBackend] = useState<string>(defaultBackend);
  const [selectedProfile, setSelectedProfile] = useState<any | null>(null);
  const [showSettingsMenu, setShowSettingsMenu] = useState<boolean>(false);
  const [streamMode, setStreamMode] = useState<boolean>(() => {
    // ローカルストレージからストリームモード設定を取得
    const savedStreamMode = localStorage.getItem("streamMode");
    return savedStreamMode !== null ? savedStreamMode === "true" : true; // デフォルトはtrue
  });

  // 初期化時にデフォルト値をログに出力
  useEffect(() => {
    logger.info(`初期化時のデフォルト値: モデル=${defaultModel}, バックエンド=${defaultBackend}`);
  }, []);

  // コンポーネントの読み込み時にローカルストレージから選択されたモデルとバックエンド、セッションIDを取得
  useEffect(() => {
    const savedModel = localStorage.getItem("selectedModel");
    const savedBackend = localStorage.getItem("selectedBackend");
    const savedPlugins = localStorage.getItem("selectedPlugins");
    const savedUseTools = localStorage.getItem("useTools");
    const savedSessionId = localStorage.getItem("currentSessionId");

    if (savedModel) {
      setSelectedModel(savedModel);
    }

    if (savedBackend) {
      setSelectedBackend(savedBackend);
      // バックエンド戦略を設定
      serviceFactory.setBackendStrategy(savedBackend);
    }

    if (savedPlugins) {
      try {
        const plugins = JSON.parse(savedPlugins);
        setSelectedPlugins(plugins);
      } catch (e) {
        logger.error("Failed to parse saved plugins", e);
      }
    }

    if (savedUseTools) {
      setUseTools(savedUseTools === "true");
    }

    // ローカルストレージからセッションIDを復元
    if (savedSessionId) {
      logger.info(`ローカルストレージからセッションIDを復元: ${savedSessionId}`);
      storeSetCurrentSessionId(savedSessionId);
    }
  }, []);

  // currentSessionIdの変更を監視し、セッションメッセージを読み込む
  useEffect(() => {
    const loadSessionMessages = async () => {
      if (storeCurrentSessionId) {
        logger.info(`Loading messages for session: ${storeCurrentSessionId}`);
        setLoading(true);
        setError(null);

        try {
          // 現在のメッセージをクリア
          clearMessages();

          // セッションメッセージを取得
          const messages = await apiService.fetchChatMessages(
            storeCurrentSessionId,
          );
          logger.info(
            `Fetched ${messages.length} messages for session ${storeCurrentSessionId}`,
          );

          // 各メッセージの詳細をログに出力（デバッグ用）
          messages.forEach((msg, index) => {
            logger.debug(`Message ${index + 1}/${messages.length}:`, {
              id: msg.id,
              role: msg.role,
              contentPreview: msg.content.substring(0, 30) + (msg.content.length > 30 ? "..." : ""),
              created_at: msg.created_at
            });
          });

          // メッセージをフロントエンド形式に変換し、グローバルステートに追加
          if (messages && messages.length > 0) {
            // 時間順にソート
            messages.sort((a, b) => {
              return (
                new Date(a.created_at).getTime() -
                new Date(b.created_at).getTime()
              );
            });

            logger.info(`Messages sorted by time, adding to store...`);

            // メッセージをグローバルステートに追加
            messages.forEach((msg, index) => {
              const message = {
                role: msg.role as "user" | "assistant" | "system",
                content: msg.content,
              };
              storeAddMessage(message);
              logger.debug(`Added message ${index + 1}/${messages.length} to store:`, {
                role: message.role,
                contentPreview: message.content.substring(0, 30) + (message.content.length > 30 ? "..." : "")
              });
            });

            // 一番下までスクロール
            setTimeout(() => scrollToBottom(), 100);
          } else {
            logger.warn(`No messages found for session ${storeCurrentSessionId}`);
          }
        } catch (error) {
          logger.error(
            `Error loading messages for session ${storeCurrentSessionId}:`,
            error,
          );
          setError(
            `会話メッセージの読み込みに失敗しました: ${error instanceof Error ? error.message : String(error)}`,
          );
        } finally {
          setLoading(false);
        }
      }
    };

    loadSessionMessages();
  }, [storeCurrentSessionId]);

  /**
   * メッセージが有効かどうかを検証
   * @param content メッセージ内容
   * @returns メッセージが有効かどうか
   */
  const isValidMessage = (content: string): boolean => {
    return !!content.trim();
  };

  /**
   * ユーザーメッセージオブジェクトを作成
   * @param content メッセージ内容
   * @returns ユーザーメッセージオブジェクト
   */
  const createUserMessage = (content: string): Message => {
    return { role: "user", content };
  };

  /**
   * メッセージをUIに追加
   * @param message メッセージオブジェクト
   */
  const addMessageToUI = (message: Message): void => {
    storeAddMessage(message);
    logger.debug("Message added to UI", {
      role: message.role,
      contentLength: message.content.length,
    });
  };

  /**
   * 最後のメッセージを更新
   * @param message メッセージオブジェクト
   */
  const updateLastMessage = (message: Message): void => {
    const updatedMessages = [...storeMessages];
    if (updatedMessages.length > 0) {
      updatedMessages[updatedMessages.length - 1] = message;
      useChatStore.setState({ messages: updatedMessages });
      logger.debug("Last message updated", {
        role: message.role,
        contentLength: message.content.length,
      });
    }
  };

  /**
   * 最初のユーザーメッセージかどうかを確認
   * @returns 最初のユーザーメッセージかどうか
   */
  const isFirstUserMessage = (): boolean => {
    // ES5との互換性のためにfilterメソッドではなくループを使用
    for (let i = 0; i < storeMessages.length; i++) {
      if (storeMessages[i].role === "user") {
        return false;
      }
    }
    return true;
  };

  /**
   * メッセージを既存のセッションに保存
   * @param sessionId セッションID
   * @param message メッセージオブジェクト
   * @returns 保存されたメッセージオブジェクト、またはnull（エラー時）
   */
  const saveMessageToSession = async (
    sessionId: string,
    message: Message,
  ): Promise<any> => {
    try {
      logger.info(`Saving message to session ${sessionId}`, {
        role: message.role,
        contentPreview: message.content.substring(0, 30) + (message.content.length > 30 ? "..." : "")
      });

      const result = await apiService.addChatMessage(
        sessionId,
        message.role,
        message.content,
      );

      if (result) {
        logger.debug("Message saved to session successfully", {
          sessionId,
          messageId: result.id,
          role: message.role,
          contentPreview: message.content.substring(0, 30) + (message.content.length > 30 ? "..." : "")
        });
        return result;
      } else {
        logger.warn("Failed to save message to session (no result returned)", {
          sessionId,
          role: message.role
        });
        return null;
      }
    } catch (error) {
      logger.error(`Error saving message to session ${sessionId}`, error);

      // エラーの詳細をログに出力
      if (error.response) {
        logger.error("Error response details:", {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        });
      }

      return null;
    }
  };

  /**
   * 新しいセッションを作成
   * @param userMessage ユーザーメッセージ
   */
  const createNewSession = async (userMessage: Message): Promise<void> => {
    try {
      // アシスタントメッセージがすでにあるか確認、実際の対話がある場合のみセッションを作成
      // ES5との互換性のためにsomeメソッドではなくループを使用
      let hasAssistantMessage = false;
      for (let i = 0; i < storeMessages.length; i++) {
        if (
          storeMessages[i].role === "assistant" &&
          storeMessages[i].content.trim() !== ""
        ) {
          hasAssistantMessage = true;
          break;
        }
      }

      // アシスタントメッセージがまだない場合、セッションを作成せず、アシスタントの応答を受け取った後に作成する
      if (!hasAssistantMessage) {
        logger.info(
          "No assistant message yet, will create session after receiving response",
        );
        return;
      }

      // 仮のタイトルを初期化
      let sessionTitle = "New Chat";

      // セッションを作成
      logger.info(
        `Creating new chat session with temporary title: ${sessionTitle}`,
      );
      const session = await apiService.createChatSession(sessionTitle);

      if (session) {
        logger.info("New chat session created", { sessionId: session.id });

        // 現在のセッションIDを設定
        storeSetCurrentSessionId(session.id);

        // セッションIDをローカルストレージに保存
        localStorage.setItem("currentSessionId", session.id);
        logger.info(`セッションIDをローカルストレージに保存: ${session.id}`);

        // 現在のすべてのメッセージを新しいセッションに保存
        const allMessages = [...storeMessages, userMessage];
        logger.info("Saving all current messages to new session", {
          sessionId: session.id,
          messagesCount: allMessages.length,
        });

        // 非同期ですべてのメッセージを保存
        saveConversationHistory(session.id, allMessages)
          .then((success) => {
            if (success) {
              logger.info("Successfully saved all messages to new session", {
                sessionId: session.id,
              });
              updateSessionTitle(session.id, userMessage.content);
            } else {
              logger.error("Failed to save all messages to new session", {
                sessionId: session.id,
              });
            }
          })
          .catch((error) => {
            logger.error("Error saving conversation history", {
              sessionId: session.id,
              error,
            });
          });
      }
    } catch (error) {
      logger.error("Error creating chat session", error);
      // セッションの作成が失敗しても、メッセージの送信を続行
    }
  };

  /**
   * セッションタイトルを更新
   * @param sessionId セッションID
   * @param content ユーザーメッセージ内容
   */
  const updateSessionTitle = async (
    sessionId: string,
    content: string,
  ): Promise<void> => {
    try {
      // 現在選択されているモデルをログに出力
      logger.info(`セッションタイトル生成に使用するモデル: ${selectedModel}`);

      // AIを使用してセッションタイトルを生成
      // 現在の会話内容を取得
      const contextMessages: Message[] = [
        { role: "user", content: content } // 最初のユーザーメッセージを使用
      ];

      // 選択されたモデルを使用してタイトルを生成
      const title = await apiService.generateSessionTitle(contextMessages, selectedModel);
      logger.info(`AIが生成したセッションタイトル: ${title}`);

      // セッションタイトルを更新
      const success = await apiService.updateSessionTitle(sessionId, title);

      if (success) {
        logger.info(`セッションタイトルを正常に更新しました: ${title}`, {
          sessionId,
          model: selectedModel
        });
      } else {
        logger.error("セッションタイトルの更新に失敗しました", { sessionId });
      }
    } catch (error) {
      logger.error("セッションタイトルの更新中にエラーが発生しました", { sessionId, error });

      // エラーが発生した場合は、ユーザーメッセージをそのままタイトルとして使用
      try {
        // ユーザーメッセージをタイトルとして使用
        const fallbackTitle = content.length > 30 ? content.substring(0, 30) + "..." : content;
        logger.info(`フォールバックタイトルを使用: ${fallbackTitle}`);

        // セッションタイトルを更新
        await apiService.updateSessionTitle(sessionId, fallbackTitle);
      } catch (fallbackError) {
        logger.error("フォールバックタイトルの設定に失敗しました", { sessionId, fallbackError });
      }
    }
  };

  /**
   * 会話履歴全体をセッションに保存
   * @param sessionId セッションID
   * @param messages メッセージリスト
   */
  const saveConversationHistory = async (
    sessionId: string,
    messages: Message[],
  ): Promise<boolean> => {
    try {
      logger.info(
        `Saving entire conversation history to session ${sessionId}`,
        { messagesCount: messages.length },
      );

      // 各メッセージの詳細をログに出力（デバッグ用）
      messages.forEach((msg, index) => {
        logger.debug(`Message to save ${index + 1}/${messages.length}:`, {
          role: msg.role,
          contentPreview: msg.content.substring(0, 30) + (msg.content.length > 30 ? "..." : "")
        });
      });

      // 改良されたメッセージ保存ロジック
      let successCount = 0;
      let failureCount = 0;

      // 順番にすべてのメッセージを保存
      for (let i = 0; i < messages.length; i++) {
        const message = messages[i];
        try {
          // 最初のメッセージの場合、遅延を追加し、以前のリクエストと競合しないようにする
          if (i === 0) {
            await new Promise((resolve) => setTimeout(resolve, 200));
          }

          // 各メッセージ間に小さな遅延を追加し、リクエストが速すぎないようにする
          if (i > 0) {
            await new Promise((resolve) => setTimeout(resolve, 100));
          }

          logger.info(`Saving message ${i + 1}/${messages.length} to session ${sessionId}`, {
            role: message.role,
            contentPreview: message.content.substring(0, 30) + (message.content.length > 30 ? "..." : "")
          });

          const result = await apiService.addChatMessage(
            sessionId,
            message.role,
            message.content,
          );

          if (result) {
            successCount++;
            logger.debug(`Saved message ${i + 1}/${messages.length} successfully`, {
              sessionId,
              messageId: result.id,
              role: message.role,
              contentPreview: message.content.substring(0, 30) + (message.content.length > 30 ? "..." : ""),
            });
          } else {
            failureCount++;
            logger.error(`Failed to save message ${i + 1}/${messages.length}`, {
              sessionId,
              role: message.role,
            });
          }
        } catch (error) {
          failureCount++;
          logger.error(`Error saving message ${i + 1}/${messages.length}`, {
            sessionId,
            role: message.role,
            error,
          });
        }
      }

      // 保存結果の要約を返す
      logger.info(
        `Conversation history save complete: ${successCount} messages saved, ${failureCount} failed`,
        { sessionId },
      );

      // 保存が完了したら、セッションからメッセージを再取得して確認
      try {
        const savedMessages = await apiService.fetchChatMessages(sessionId);
        logger.info(`Verification: Fetched ${savedMessages.length} messages from session ${sessionId}`);

        // 各メッセージの詳細をログに出力（デバッグ用）
        savedMessages.forEach((msg, index) => {
          logger.debug(`Saved message ${index + 1}/${savedMessages.length}:`, {
            id: msg.id,
            role: msg.role,
            contentPreview: msg.content.substring(0, 30) + (msg.content.length > 30 ? "..." : ""),
            created_at: msg.created_at
          });
        });
      } catch (verifyError) {
        logger.error(`Error verifying saved messages for session ${sessionId}:`, verifyError);
      }

      return failureCount === 0; // すべてのメッセージが正常に保存された場合のみ true を返す
    } catch (error) {
      logger.error("Error saving conversation history", { sessionId, error });
      return false;
    }
  };

  /**
   * ストリームレスポンスを処理
   * @param response レスポンスオブジェクト
   * @param assistantMessage アシスタントメッセージオブジェクト
   */
  const handleStreamResponse = (
    response: Response,
    assistantMessage: Message,
  ): void => {
    logger.info("Handling stream response");

    // バックエンド戦略を使用してストリームを処理
    apiService.handleStreamResponse(
      response,
      (chunk) => {
        // ストリームチャンクを処理
        try {
          // エラー情報を含むかチェック
          if (chunk.error) {
            logger.warn("エラー情報を含むチャンクを受信しました", { error: chunk.error });

            // エラーメッセージを抽出
            let errorContent = "";
            if (chunk.choices && chunk.choices.length > 0 && chunk.choices[0].delta && chunk.choices[0].delta.content) {
              errorContent = chunk.choices[0].delta.content;
            } else {
              errorContent = `エラー: ${chunk.error.message || "不明なエラーが発生しました"}`;
            }

            // アシスタントメッセージにエラーメッセージを追加
            assistantMessage.content += errorContent;
            updateLastMessage(assistantMessage);

            // 一番下までスクロール
            scrollToBottom();
            return;
          }

          let content = "";

          // コンテンツを抽出
          if (
            chunk.choices &&
            chunk.choices.length > 0 &&
            chunk.choices[0].delta &&
            chunk.choices[0].delta.content
          ) {
            content = chunk.choices[0].delta.content;
          } else if (chunk.content) {
            content = chunk.content;
          } else if (chunk.message && chunk.message.content) {
            content = chunk.message.content;
          }

          if (content) {
            // アシスタントメッセージを更新
            assistantMessage.content += content;
            updateLastMessage(assistantMessage);

            // 一番下までスクロール
            scrollToBottom();
          }
        } catch (error) {
          logger.error("Error processing stream chunk", error);

          // エラーメッセージをアシスタントメッセージに追加
          const errorContent = `チャンク処理エラー: ${error instanceof Error ? error.message : "不明なエラー"}`;
          assistantMessage.content += errorContent;
          updateLastMessage(assistantMessage);

          // 一番下までスクロール
          scrollToBottom();
        }
      },
      () => {
        // ストリーム完了
        logger.info("Stream completed");
        setLoading(false);

        // 最初のユーザーメッセージの場合、新しいセッションを作成
        if (isFirstUserMessage() && !storeCurrentSessionId) {
          // ユーザーの最初のメッセージを取得
          let firstUserMessage: Message | null = null;
          for (let i = 0; i < storeMessages.length; i++) {
            if (storeMessages[i].role === "user") {
              firstUserMessage = storeMessages[i];
              break;
            }
          }

          if (firstUserMessage) {
            createNewSession(firstUserMessage);
          }
        } else if (storeCurrentSessionId) {
          // 既存のセッションIDがある場合、アシスタントメッセージをセッションに保存
          saveMessageToSession(storeCurrentSessionId, assistantMessage);
        }
      },
      (error) => {
        // ストリームエラー
        logger.error("ストリームエラー", error);

        // エラーメッセージをフォーマット
        let errorMessage = error.message || "不明なエラーが発生しました";

        // エラーメッセージがストリームチャンクのエラープロパティから来ている場合、それを使用
        if (assistantMessage.content.includes("エラー:") ||
            assistantMessage.content.includes("Error:") ||
            assistantMessage.content.includes("解析エラー:") ||
            assistantMessage.content.includes("ストリーム読み取りエラー:")) {
          // エラーメッセージが既に表示されている場合は、追加のエラーメッセージを表示しない
          setError(null);
        } else {
          // エラーメッセージが表示されていない場合は、エラーメッセージを表示
          setError(`エラー: ${errorMessage}`);

          // アシスタントメッセージが空の場合、エラーメッセージを追加
          if (!assistantMessage.content) {
            assistantMessage.content = `エラー: ${errorMessage}`;
            updateLastMessage(assistantMessage);
          }
        }

        setLoading(false);

        // 一番下までスクロール
        scrollToBottom();
      },
      selectedBackend,
    );
  };

  /**
   * モデルが有効かどうかを検証
   * @param modelId モデル ID
   * @returns モデルが有効かどうか
   */
  const validateModel = async (modelId: string): Promise<boolean> => {
    try {
      // 利用可能なモデルリストを取得
      const models = await apiService.fetchModels();

      // モデルが存在するか確認
      const modelExists = models.some((model) => model.id === modelId);

      if (!modelExists) {
        logger.warn(`Model ${modelId} not found in available models`);
        return false;
      }

      return true;
    } catch (error) {
      logger.error(`Error validating model ${modelId}:`, error);
      return false;
    }
  };

  /**
   * デフォルトモデルを取得
   * @returns デフォルトモデル ID
   */
  const getDefaultModel = async (): Promise<string> => {
    try {
      // 利用可能なモデルリストを取得
      const models = await apiService.fetchModels();

      // まずデフォルトとしてマークされているモデルを探す
      for (const model of models) {
        if (model.default) {
          return model.id;
        }
      }

      // デフォルトとしてマークされているモデルがない場合、最初のモデルを使用
      if (models.length > 0) {
        return models[0].id;
      }

      // 利用可能なモデルがない場合、ハードコードされたデフォルト値を返す
      return "llama3.2:3b";
    } catch (error) {
      logger.error("Error getting default model:", error);
      return "llama3.2:3b";
    }
  };

  /**
   * メッセージを送信
   * @param content メッセージ内容
   */
  const sendMessage = async (content: string): Promise<void> => {
    if (!isValidMessage(content)) {
      logger.warn("無効なメッセージ内容です");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // ユーザーメッセージを作成
      const userMessage = createUserMessage(content);
      logger.info("ユーザーメッセージを作成しました", {
        content: content.substring(0, 30) + (content.length > 30 ? "..." : ""),
        role: "user"
      });

      // UIにユーザーメッセージを追加
      addMessageToUI(userMessage);

      // 既存のセッションIDがある場合、ユーザーメッセージをセッションに保存
      if (storeCurrentSessionId) {
        logger.info(`Saving user message to existing session: ${storeCurrentSessionId}`);
        try {
          const result = await saveMessageToSession(storeCurrentSessionId, userMessage);
          logger.info(`User message saved to session: ${storeCurrentSessionId}`, { success: !!result });
        } catch (error) {
          logger.error(`Error saving user message to session: ${storeCurrentSessionId}`, error);
        }
      } else {
        logger.info(`No session ID available yet, user message will be saved after session creation`);
      }

      // アシスタントメッセージを作成（初期値は空）
      const assistantMessage: Message = {
        role: "assistant",
        content: "",
      };
      addMessageToUI(assistantMessage);

      // 一番下までスクロール
      scrollToBottom();

      // モデルが有効かどうかを検証
      let modelToUse = selectedModel;
      const isModelValid = await validateModel(modelToUse);

      if (!isModelValid) {
        logger.warn(
          `選択されたモデル ${modelToUse} は有効ではありません。デフォルトモデルを使用します`,
        );
        modelToUse = await getDefaultModel();
        // 選択されたモデルを更新
        setSelectedModel(modelToUse);
        localStorage.setItem("selectedModel", modelToUse);
      }

      // チャットオプションを準備
      const chatOptions: ChatOptions = {
        model: modelToUse,
        backend: selectedBackend,
        stream: streamMode,
        temperature: 0.7,
        top_p: 0.9,
        top_k: 50,
        max_tokens: 500,
        plugins: selectedPlugins,
        use_tools: useTools,
      };

      // 選択されたプロファイルがある場合、そのパラメータを使用
      if (selectedProfile) {
        chatOptions.temperature =
          selectedProfile.parameters.find((p: any) => p.name === "temperature")
            ?.value || 0.7;
        chatOptions.top_p =
          selectedProfile.parameters.find((p: any) => p.name === "top_p")
            ?.value || 0.9;
        chatOptions.top_k =
          selectedProfile.parameters.find((p: any) => p.name === "top_k")
            ?.value || 50;

        if (selectedProfile.model) {
          // プロファイル内のモデルが有効かどうかを検証
          const isProfileModelValid = await validateModel(
            selectedProfile.model,
          );
          if (isProfileModelValid) {
            chatOptions.model = selectedProfile.model;
          } else {
            logger.warn(
              `プロファイルモデル ${selectedProfile.model} は有効ではありません。${modelToUse} を使用します`,
            );
          }
        }

        if (selectedProfile.backend) {
          chatOptions.backend = selectedProfile.backend;
        }
      }

      // セッションIDがある場合、バックエンドオプションに追加
      if (storeCurrentSessionId) {
        chatOptions.backend_options = {
          session_id: storeCurrentSessionId,
        };
      }

      // チャットリクエストを送信するためのメッセージ配列を準備
      // システムメッセージを追加
      const systemMessage: Message = {
        role: "system",
        content: "あなたは役立つAIアシスタントです。ユーザーの質問に簡潔かつ正確に答えてください。"
      };

      // 会話のコンテキストを準備
      let messagesToSend: Message[] = [];

      // システムメッセージを常に最初に追加
      messagesToSend.push(systemMessage);

      // 現在の会話履歴を追加
      // システムメッセージを除外し、最後のアシスタントメッセージ（空の場合）も除外
      const historyMessages = storeMessages.filter((msg, index) => {
        // システムメッセージを除外
        if (msg.role === "system") return false;

        // 最後のアシスタントメッセージ（空の場合）を除外
        if (msg.role === "assistant" && msg.content === "" && index === storeMessages.length - 1) return false;

        return true;
      });

      // 現在の会話履歴を追加
      messagesToSend = [...messagesToSend, ...historyMessages];

      // 最後のメッセージがユーザーのものでない場合、現在のユーザーメッセージを追加
      if (messagesToSend.length === 0 || messagesToSend[messagesToSend.length - 1].role !== "user") {
        messagesToSend.push(userMessage);
      }

      // メッセージ配列の詳細をログに出力
      logger.info("送信するメッセージ配列", {
        messagesCount: messagesToSend.length,
        roles: messagesToSend.map(msg => msg.role).join(", "),
        model: chatOptions.model,
        backend: chatOptions.backend,
        stream: chatOptions.stream
      });

      // メッセージ配列が有効か確認
      if (messagesToSend.length < 2) { // システムメッセージと少なくとも1つのユーザーメッセージが必要
        const errorMsg = "メッセージ配列が不完全です。システムメッセージとユーザーメッセージが必要です。";
        logger.error(errorMsg, { messagesToSend });
        throw new Error(errorMsg);
      }

      // ユーザーメッセージが存在するか確認
      const hasUserMessage = messagesToSend.some(msg => msg.role === "user");
      if (!hasUserMessage) {
        const errorMsg = "メッセージ配列にユーザーメッセージがありません。";
        logger.error(errorMsg, { messagesToSend });
        throw new Error(errorMsg);
      }

      // APIリクエストを送信
      const response = await apiService.sendChatRequest(
        messagesToSend,
        chatOptions,
      );

      // レスポンスを処理
      if (response instanceof Response) {
        // ストリームレスポンス
        handleStreamResponse(response, assistantMessage);
      } else {
        // 非ストリームレスポンス
        logger.info("Received non-streaming response");

        // アシスタントメッセージを更新
        assistantMessage.content = response.choices[0].message.content;
        updateLastMessage(assistantMessage);

        // 最初のユーザーメッセージの場合、新しいセッションを作成
        if (isFirstUserMessage() && !storeCurrentSessionId) {
          createNewSession(userMessage);
        } else if (storeCurrentSessionId) {
          // 既存のセッションIDがある場合、アシスタントメッセージをセッションに保存
          saveMessageToSession(storeCurrentSessionId, assistantMessage);
        }

        setLoading(false);

        // 一番下までスクロール
        scrollToBottom();
      }
    } catch (error) {
      logger.error("Error sending message", error);
      setLoading(false);
      setError(
        `Error: ${error instanceof Error ? error.message : String(error)}`,
      );

      // 一番下までスクロール
      scrollToBottom();
    }
  };

  /**
   * 一番下までスクロール
   */
  const scrollToBottom = () => {
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };

  /**
   * メッセージをクリア
   */
  const handleClearMessages = () => {
    // グローバルステート内のメッセージをクリア
    clearMessages();

    // 現在のセッションIDをクリア
    storeSetCurrentSessionId("");

    // ローカルストレージからセッションIDを削除
    localStorage.removeItem("currentSessionId");
    logger.info("ローカルストレージからセッションIDを削除しました");

    // エラーをクリア
    setError(null);

    logger.info("Messages cleared");
  };

  /**
   * モデルを設定
   * @param model モデル名
   */
  const setModel = (model: string) => {
    // ローカルステートを更新
    setSelectedModel(model);

    // ローカルストレージに保存（既にModelSelector内で保存されている可能性があるが、念のため）
    localStorage.setItem("selectedModel", model);

    // ログ出力
    logger.info(`Model set to ${model}`);
  };

  /**
   * バックエンドを設定
   * @param backend バックエンド名
   */
  const setBackend = (backend: string) => {
    setSelectedBackend(backend);
    localStorage.setItem("selectedBackend", backend);

    // バックエンド戦略を設定
    serviceFactory.setBackendStrategy(backend);

    logger.info(`Backend set to ${backend}`);
  };

  /**
   * プロファイルを設定
   * @param profile プロファイル
   */
  const setProfile = (profile: any | null) => {
    setSelectedProfile(profile);
    logger.info(`Profile set to ${profile ? profile.name : "none"}`);
  };

  /**
   * 設定メニューを切り替え
   */
  const toggleSettingsMenu = () => {
    setShowSettingsMenu(!showSettingsMenu);
  };

  /**
   * ストリームモードを切り替える
   * @param enabled ストリームモードを有効にするかどうか
   */
  const toggleStreamMode = (enabled: boolean): void => {
    setStreamMode(enabled);
    localStorage.setItem("streamMode", enabled.toString());
    logger.info(`ストリームモードを${enabled ? '有効' : '無効'}に切り替えました`);
  };

  /**
   * プラグインを変更する
   * @param plugins 選択されたプラグインの配列
   */
  const setPlugins = (plugins: string[]): void => {
    setSelectedPlugins(plugins);
    localStorage.setItem("selectedPlugins", JSON.stringify(plugins));
    logger.info(`プラグインを更新しました: ${plugins.join(', ')}`);
  };

  /**
   * ツール使用を切り替える
   * @param enabled ツールを有効にするかどうか
   */
  const toggleUseTools = (enabled: boolean): void => {
    setUseTools(enabled);
    localStorage.setItem("useTools", enabled.toString());
    logger.info(`ツール使用を${enabled ? '有効' : '無効'}に切り替えました`);
  };

  return {
    messages: storeMessages,
    loading,
    error,
    selectedModel,
    selectedBackend,
    selectedProfile,
    selectedPlugins,
    useTools,
    showSettingsMenu,
    streamMode,
    messagesEndRef,
    sendMessage,
    clearMessages: handleClearMessages,
    setModel,
    setBackend,
    setProfile,
    setPlugins,
    toggleUseTools,
    toggleStreamMode,
    toggleSettingsMenu,
  };
};
