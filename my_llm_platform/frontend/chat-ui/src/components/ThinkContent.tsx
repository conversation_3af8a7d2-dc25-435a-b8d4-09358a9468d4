import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { useTranslation } from 'react-i18next';

interface ThinkContentProps {
  content: string;
}

/**
 * 思考内容コンポーネント
 * モデルの思考プロセスを表示・非表示できるコンポーネント
 */
const ThinkContent: React.FC<ThinkContentProps> = ({ content }) => {
  const { t } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  // 思考内容を抽出する
  const extractThinkContent = (text: string): { thinkContent: string | null; visibleContent: string } => {
    const thinkRegex = /<think>([\s\S]*?)<\/think>/;
    const match = text.match(thinkRegex);
    
    if (match && match[1]) {
      // <think>タグがある場合
      const thinkContent = match[1].trim();
      const visibleContent = text.replace(thinkRegex, '').trim();
      return { thinkContent, visibleContent };
    }
    
    // <think>タグがない場合
    return { thinkContent: null, visibleContent: text };
  };

  const { thinkContent, visibleContent } = extractThinkContent(content);

  // 思考内容がない場合は通常のマークダウンを表示
  if (!thinkContent) {
    return <ReactMarkdown>{content}</ReactMarkdown>;
  }

  return (
    <div className="think-content-wrapper">
      <ReactMarkdown>{visibleContent}</ReactMarkdown>
      
      {thinkContent && (
        <div className="think-container">
          <button 
            className={`think-toggle ${isExpanded ? 'expanded' : 'collapsed'}`}
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? t('hide_thinking') : t('show_thinking')}
          </button>
          
          {isExpanded && (
            <div className="think-box">
              <div className="think-header">{t('model_thinking')}</div>
              <div className="think-body">
                <ReactMarkdown>{thinkContent}</ReactMarkdown>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ThinkContent;
