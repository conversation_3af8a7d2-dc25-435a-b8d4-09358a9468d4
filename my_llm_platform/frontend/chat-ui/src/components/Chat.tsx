import React, { useState, useRef, useEffect } from "react";
import ReactMarkdown from "react-markdown";
// 通常の SyntaxHighlighter ではなく PrismLight を使用
import SyntaxHighlighter from "react-syntax-highlighter";
import { tomorrow } from "react-syntax-highlighter/dist/esm/styles/hljs";
import { useChatStore } from "../store/chatStore";
import { useTranslation } from "react-i18next";
import { API_CONFIG } from '../config/api';
import { filterModelResponse } from "../utils/responseFilter";

const Chat: React.FC = () => {
  const { t } = useTranslation();
  const {
    messages,
    loading,
    error,
    currentStreamingMessage,
    addMessage,
    setLoading,
    setError,
    setCurrentStreamingMessage,
  } = useChatStore();

  const [input, setInput] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, currentStreamingMessage]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    const userMessage = {
      role: "user" as const,
      content: input,
    };

    addMessage(userMessage);
    setInput("");
    setLoading(true);
    setError(undefined);

    try {
      // APIアダプターを使用してAPIエンドポイントを取得
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || "http://localhost:8001";
      const apiPrefix = import.meta.env.VITE_API_PREFIX || "/api";
      const defaultModel = API_CONFIG.DEFAULT_MODEL;

      console.log(`API Base URL: ${apiBaseUrl}`);
      console.log(`API Prefix: ${apiPrefix}`);
      console.log(`Default Model: ${defaultModel}`);

      // 標準のOpenAI互換エンドポイントを使用
      const endpoints = [
        `${apiBaseUrl}${apiPrefix}/v1/chat/completions`,
      ];

      let response = null;
      let errorMessage = "";

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying endpoint: ${endpoint}`);

          response = await fetch(endpoint, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              model: defaultModel,
              messages: [...messages, userMessage],
              stream: true,
            }),
          });

          if (response.ok) {
            console.log(`Successfully connected to ${endpoint}`);
            break;
          } else {
            const errorText = await response.text();
            console.error(`Error from ${endpoint}: ${response.status}, ${errorText}`);
            errorMessage += `${endpoint}: ${response.status}, ${errorText}\n`;
          }
        } catch (error) {
          console.error(`Error connecting to ${endpoint}:`, error);
          errorMessage += `${endpoint}: ${error}\n`;
        }
      }

      if (!response || !response.ok) {
        throw new Error(`Failed to connect to any API endpoint: ${errorMessage}`);
      }

      console.log("Response received, starting to process stream");

      const reader = response.body?.getReader();
      if (!reader) throw new Error("No reader available");

      let streamedContent = "";
      let buffer = "";

      console.log("Starting to read stream");

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          console.log("Stream reading complete");
          break;
        }

        const text = new TextDecoder().decode(value, { stream: true });
        console.log("Received chunk:", text);

        // バッファに新しいデータを追加
        buffer += text;

        // 完全な行を処理
        const lines = buffer.split("\n");
        // 最後の行は不完全な可能性があるのでバッファに保持
        buffer = lines.pop() || "";

        for (const line of lines) {
          console.log("Processing line:", line);

          if (line.trim() === "") continue;

          if (line.startsWith("data: ")) {
            const data = line.substring(6).trim();
            console.log("Extracted data:", data);

            if (data === "[DONE]") {
              console.log("Received [DONE] signal");
              break;
            }

            try {
              const parsed = JSON.parse(data);
              console.log("Parsed JSON:", parsed);

              // OpenAI形式のレスポンス処理
              if (parsed.choices && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                const content = parsed.choices[0].delta.content;
                streamedContent += content;
                // ストリーミング中も特殊トークンをフィルタリング
                const filteredContent = filterModelResponse(streamedContent);
                setCurrentStreamingMessage(filteredContent);
              }
              // 直接contentフィールドを持つ形式の処理
              else if (parsed.content) {
                streamedContent += parsed.content;
                // ストリーミング中も特殊トークンをフィルタリング
                const filteredContent = filterModelResponse(streamedContent);
                setCurrentStreamingMessage(filteredContent);
              }
              // Ollama形式のレスポンス処理
              else if (parsed.message && parsed.message.content) {
                streamedContent += parsed.message.content;
                // ストリーミング中も特殊トークンをフィルタリング
                const filteredContent = filterModelResponse(streamedContent);
                setCurrentStreamingMessage(filteredContent);
              }
              // その他の形式
              else if (typeof parsed === 'string') {
                streamedContent += parsed;
                // ストリーミング中も特殊トークンをフィルタリング
                const filteredContent = filterModelResponse(streamedContent);
                setCurrentStreamingMessage(filteredContent);
              }
            } catch (e) {
              // JSON解析エラー
              console.error("Error parsing JSON:", e, "Raw data:", data);
            }
          } else {
            // Ollamaのレスポンス形式の場合、各行が直接JSON
            try {
              const parsed = JSON.parse(line);
              console.log("Parsed direct JSON:", parsed);

              if (parsed.message && parsed.message.content) {
                streamedContent += parsed.message.content;
                // ストリーミング中も特殊トークンをフィルタリング
                const filteredContent = filterModelResponse(streamedContent);
                setCurrentStreamingMessage(filteredContent);
              } else if (parsed.response) {
                streamedContent += parsed.response;
                // ストリーミング中も特殊トークンをフィルタリング
                const filteredContent = filterModelResponse(streamedContent);
                setCurrentStreamingMessage(filteredContent);
              }
            } catch (e) {
              // JSONでない場合は無視
              console.log("Line is not JSON, ignoring:", line);
            }
          }
        }
      }

      // 最終的なメッセージを追加する前に特殊トークンをフィルタリング
      const filteredContent = filterModelResponse(streamedContent);
      addMessage({
        role: "assistant",
        content: filteredContent,
      });
    } catch (error) {
      console.error("Error:", error);
      setError(t("chat.error"));
    } finally {
      setLoading(false);
      setCurrentStreamingMessage("");
    }
  };

  const MessageContent: React.FC<{ content: string }> = ({ content }) => (
    <ReactMarkdown
      components={{
        code({ className, children, ...props }) {
          const match = /language-(\w+)/.exec(className || "");
          return match ? (
            <SyntaxHighlighter
              // @ts-ignore - タイプ問題は一時的に無視
              style={tomorrow}
              language={match[1]}
              PreTag="div"
              {...props}
            >
              {String(children).replace(/\n$/, "")}
            </SyntaxHighlighter>
          ) : (
            <code className={className} {...props}>
              {children}
            </code>
          );
        },
      }}
    >
      {content}
    </ReactMarkdown>
  );

  return (
    <div className="flex flex-col h-screen bg-gray-100">
      <div className="flex-1 overflow-y-auto p-4">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`mb-4 ${
              message.role === "user" ? "text-right" : "text-left"
            }`}
          >
            <div
              className={`inline-block p-4 rounded-lg ${
                message.role === "user"
                  ? "bg-blue-500 text-white"
                  : "bg-white text-gray-800"
              }`}
            >
              <MessageContent content={message.content} />
            </div>
          </div>
        ))}
        {loading && currentStreamingMessage && (
          <div className="text-left">
            <div className="inline-block p-4 rounded-lg bg-white text-gray-800">
              <MessageContent content={currentStreamingMessage} />
            </div>
          </div>
        )}
        {error && <div className="text-center text-red-500 mb-4">{error}</div>}
        <div ref={messagesEndRef} />
      </div>
      <form onSubmit={handleSubmit} className="p-4 bg-white border-t">
        <div className="flex space-x-4">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            className="flex-1 p-2 border rounded-lg"
            placeholder={t("chat.input.placeholder")}
            disabled={loading}
          />
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? t("chat.sending") : t("chat.send")}
          </button>
        </div>
      </form>
    </div>
  );
};

export default Chat;
