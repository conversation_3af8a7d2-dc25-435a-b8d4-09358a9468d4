import React from "react";
// import { useTranslation } from "react-i18next";
import ReactMarkdown from "react-markdown";
import Syntax<PERSON>ighlighter from "react-syntax-highlighter";
import { docco } from "react-syntax-highlighter/dist/esm/styles/hljs";
import { Message } from "../types/chat";
import { useAuthStore } from "../store/authStore";

interface MessageListProps {
  messages: Message[];
}

const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  // 暫定的に翻訳機能は不要
  // const { t } = useTranslation();
  const user = useAuthStore((state) => state.user);

  // デバッグログを追加して、レンダリングされるメッセージを確認
  console.log("Rendering MessageList with messages:", JSON.stringify(messages));

  if (!messages || messages.length === 0) {
    console.log("表示するメッセージがありません");
    return (
      <div className="empty-messages">
        {/* 空のメッセージの場合、何も表示しません。ウェルカムメッセージはチャット画面に表示されます */}
      </div>
    );
  }

  return (
    <div className="message-list">
      {messages.map((message, index) => (
        <div
          key={index}
          className={`message ${message.role === "user" ? "user-message" : "assistant-message"}`}
        >
          <div className="message-content-container">
            <div
              className={`message-avatar ${message.role === "user" ? "user-avatar" : "assistant-avatar"}`}
            >
              {message.role === "user"
                ? user?.username
                  ? user.username.substring(0, 1).toUpperCase()
                  : "U"
                : "A"}
            </div>
            <div className="message-content">
              <ReactMarkdown
                components={{
                  code({ className, children, ...props }) {
                    const match = /language-(\w+)/.exec(className || "");
                    return match ? (
                      <SyntaxHighlighter
                        // @ts-ignore - doccoスタイルの型の互換性問題
                        style={docco}
                        language={match[1]}
                        PreTag="div"
                        {...props}
                      >
                        {String(children).replace(/\n$/, "")}
                      </SyntaxHighlighter>
                    ) : (
                      <code className={className} {...props}>
                        {children}
                      </code>
                    );
                  },
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default MessageList;
