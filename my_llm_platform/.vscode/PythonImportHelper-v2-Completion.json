[{"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Header", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "status", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "status", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "status", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "status", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "FastAPI", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "OAuth2PasswordBearer", "importPath": "fastapi.security", "description": "fastapi.security", "isExtraImport": true, "detail": "fastapi.security", "documentation": {}}, {"label": "OAuth2PasswordRequestForm", "importPath": "fastapi.security", "description": "fastapi.security", "isExtraImport": true, "detail": "fastapi.security", "documentation": {}}, {"label": "JWTError", "importPath": "jose", "description": "jose", "isExtraImport": true, "detail": "jose", "documentation": {}}, {"label": "jwt", "importPath": "jose", "description": "jose", "isExtraImport": true, "detail": "jose", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON>", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "timezone", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Session", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "sessionmaker", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "relationship", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "Session", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "KeycloakOpenID", "importPath": "keycloak", "description": "keycloak", "isExtraImport": true, "detail": "keycloak", "documentation": {}}, {"label": "ABC", "importPath": "abc", "description": "abc", "isExtraImport": true, "detail": "abc", "documentation": {}}, {"label": "abstractmethod", "importPath": "abc", "description": "abc", "isExtraImport": true, "detail": "abc", "documentation": {}}, {"label": "requests", "kind": 6, "isExtraImport": true, "importPath": "requests", "description": "requests", "detail": "requests", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "logging.config", "kind": 6, "isExtraImport": true, "importPath": "logging.config", "description": "logging.config", "detail": "logging.config", "documentation": {}}, {"label": "yaml", "kind": 6, "isExtraImport": true, "importPath": "yaml", "description": "yaml", "detail": "yaml", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "Counter", "importPath": "prometheus_client", "description": "prometheus_client", "isExtraImport": true, "detail": "prometheus_client", "documentation": {}}, {"label": "UnstructuredFileLoader", "importPath": "langchain.document_loaders", "description": "langchain.document_loaders", "isExtraImport": true, "detail": "langchain.document_loaders", "documentation": {}}, {"label": "Hugging<PERSON><PERSON><PERSON>mbe<PERSON><PERSON>", "importPath": "langchain_huggingface", "description": "langchain_huggingface", "isExtraImport": true, "detail": "langchain_huggingface", "documentation": {}}, {"label": "QdrantClient", "importPath": "qdrant_client", "description": "qdrant_client", "isExtraImport": true, "detail": "qdrant_client", "documentation": {}}, {"label": "models", "importPath": "qdrant_client.http", "description": "qdrant_client.http", "isExtraImport": true, "detail": "qdrant_client.http", "documentation": {}}, {"label": "psutil", "kind": 6, "isExtraImport": true, "importPath": "psutil", "description": "psutil", "detail": "psutil", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Celery", "importPath": "celery", "description": "celery", "isExtraImport": true, "detail": "celery", "documentation": {}}, {"label": "BaseSettings", "importPath": "pydantic_settings", "description": "pydantic_settings", "isExtraImport": true, "detail": "pydantic_settings", "documentation": {}}, {"label": "create_async_engine", "importPath": "sqlalchemy.ext.asyncio", "description": "sqlalchemy.ext.asyncio", "isExtraImport": true, "detail": "sqlalchemy.ext.asyncio", "documentation": {}}, {"label": "AsyncSession", "importPath": "sqlalchemy.ext.asyncio", "description": "sqlalchemy.ext.asyncio", "isExtraImport": true, "detail": "sqlalchemy.ext.asyncio", "documentation": {}}, {"label": "declarative_base", "importPath": "sqlalchemy.ext.declarative", "description": "sqlalchemy.ext.declarative", "isExtraImport": true, "detail": "sqlalchemy.ext.declarative", "documentation": {}}, {"label": "Column", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "Integer", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "String", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "DateTime", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "ForeignKey", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "Text", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "uuid", "kind": 6, "isExtraImport": true, "importPath": "uuid", "description": "uuid", "detail": "uuid", "documentation": {}}, {"label": "redis", "kind": 6, "isExtraImport": true, "importPath": "redis", "description": "redis", "detail": "redis", "documentation": {}}, {"label": "u<PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "u<PERSON><PERSON>", "description": "u<PERSON><PERSON>", "detail": "u<PERSON><PERSON>", "documentation": {}}, {"label": "CORSMiddleware", "importPath": "fastapi.middleware.cors", "description": "fastapi.middleware.cors", "isExtraImport": true, "detail": "fastapi.middleware.cors", "documentation": {}}, {"label": "StreamingResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "AsyncOpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.auth.auth", "description": "backend.auth.auth", "peekOfCode": "router = APIRouter()\nSECRET_KEY = \"your-secret-key\"\nALGORITHM = \"HS256\"\nACCESS_TOKEN_EXPIRE_MINUTES = 30\noauth2_scheme = OAuth2PasswordBearer(tokenUrl=\"token\")\*************(\"/login\")\nasync def login(form_data: OAuth2PasswordRequestForm = Depends()):\n    # ユーザー認証とトークン生成のロジック\n    pass\*************(\"/logout\")", "detail": "backend.auth.auth", "documentation": {}}, {"label": "SECRET_KEY", "kind": 5, "importPath": "backend.auth.auth", "description": "backend.auth.auth", "peekOfCode": "SECRET_KEY = \"your-secret-key\"\nALGORITHM = \"HS256\"\nACCESS_TOKEN_EXPIRE_MINUTES = 30\noauth2_scheme = OAuth2PasswordBearer(tokenUrl=\"token\")\*************(\"/login\")\nasync def login(form_data: OAuth2PasswordRequestForm = Depends()):\n    # ユーザー認証とトークン生成のロジック\n    pass\*************(\"/logout\")\nasync def logout(token: str = Depends(oauth2_scheme)):", "detail": "backend.auth.auth", "documentation": {}}, {"label": "ALGORITHM", "kind": 5, "importPath": "backend.auth.auth", "description": "backend.auth.auth", "peekOfCode": "ALGORITHM = \"HS256\"\nACCESS_TOKEN_EXPIRE_MINUTES = 30\noauth2_scheme = OAuth2PasswordBearer(tokenUrl=\"token\")\*************(\"/login\")\nasync def login(form_data: OAuth2PasswordRequestForm = Depends()):\n    # ユーザー認証とトークン生成のロジック\n    pass\*************(\"/logout\")\nasync def logout(token: str = Depends(oauth2_scheme)):\n    # ログアウト処理", "detail": "backend.auth.auth", "documentation": {}}, {"label": "ACCESS_TOKEN_EXPIRE_MINUTES", "kind": 5, "importPath": "backend.auth.auth", "description": "backend.auth.auth", "peekOfCode": "ACCESS_TOKEN_EXPIRE_MINUTES = 30\noauth2_scheme = OAuth2PasswordBearer(tokenUrl=\"token\")\*************(\"/login\")\nasync def login(form_data: OAuth2PasswordRequestForm = Depends()):\n    # ユーザー認証とトークン生成のロジック\n    pass\*************(\"/logout\")\nasync def logout(token: str = Depends(oauth2_scheme)):\n    # ログアウト処理\n    pass", "detail": "backend.auth.auth", "documentation": {}}, {"label": "oauth2_scheme", "kind": 5, "importPath": "backend.auth.auth", "description": "backend.auth.auth", "peekOfCode": "oauth2_scheme = OAuth2PasswordBearer(tokenUrl=\"token\")\*************(\"/login\")\nasync def login(form_data: OAuth2PasswordRequestForm = Depends()):\n    # ユーザー認証とトークン生成のロジック\n    pass\*************(\"/logout\")\nasync def logout(token: str = Depends(oauth2_scheme)):\n    # ログアウト処理\n    pass", "detail": "backend.auth.auth", "documentation": {}}, {"label": "get_current_user", "kind": 2, "importPath": "backend.auth.dependencies", "description": "backend.auth.dependencies", "peekOfCode": "def get_current_user(\n    authorization: str | None = Header(None),\n    keycloak_svc: KeycloakService = Depends()\n):\n    if not authorization:\n        raise HTTPException(\n            status_code=status.HTTP_401_UNAUTHORIZED,\n            detail=\"Authorizationヘッダーがありません\"\n        )\n    prefix, _, token = authorization.partition(\" \")", "detail": "backend.auth.dependencies", "documentation": {}}, {"label": "KeycloakService", "kind": 6, "importPath": "backend.auth.keycloak_service", "description": "backend.auth.keycloak_service", "peekOfCode": "class KeycloakService:\n    def __init__(self):\n        self.keycloak_openid = KeycloakOpenID(\n            server_url=settings.KEYCLOAK_SERVER_URL,\n            client_id=settings.KEYCLOAK_CLIENT_ID,\n            realm_name=settings.KEY<PERSON>OAK_REALM,\n            client_secret_key=settings.K<PERSON><PERSON><PERSON>OAK_CLIENT_SECRET\n        )\n    def decode_token(self, token: str):\n        try:", "detail": "backend.auth.keycloak_service", "documentation": {}}, {"label": "get_backend", "kind": 2, "importPath": "backend.inference.backend_selector", "description": "backend.inference.backend_selector", "peekOfCode": "def get_backend(name: str):\n    name = name.lower()\n    if name == \"vllm\":\n        return VLLMBackend()\n    elif name == \"ollama\":\n        return OllamaBackend()\n    elif name == \"x-inference\":\n        return XInferenceBackend()\n    else:\n        # デフォルトはvLLMを使用", "detail": "backend.inference.backend_selector", "documentation": {}}, {"label": "BaseLLMBackend", "kind": 6, "importPath": "backend.inference.base_backend", "description": "backend.inference.base_backend", "peekOfCode": "class BaseLLMBackend(ABC):\n    @abstractmethod\n    def generate(self, prompt: str, max_tokens: int = 128) -> str:\n        pass", "detail": "backend.inference.base_backend", "documentation": {}}, {"label": "OllamaBackend", "kind": 6, "importPath": "backend.inference.ollama_backend", "description": "backend.inference.ollama_backend", "peekOfCode": "class OllamaBackend(BaseLLMBackend):\n    def __init__(self, endpoint=\"http://localhost:11411\"):\n        self.endpoint = endpoint\n    def generate(self, prompt: str, max_tokens: int = 128) -> str:\n        headers = {\"Content-Type\": \"application/json\"}\n        payload = {\n            \"prompt\": prompt,\n            \"num_ctx\": max_tokens\n        }\n        resp = requests.post(f\"{self.endpoint}/generate\", json=payload, headers=headers)", "detail": "backend.inference.ollama_backend", "documentation": {}}, {"label": "VLLMBackend", "kind": 6, "importPath": "backend.inference.vllm_backend", "description": "backend.inference.vllm_backend", "peekOfCode": "class VLLMBackend(BaseLLMBackend):\n    def __init__(self, endpoint=\"http://localhost:8001/v1\"):\n        self.endpoint = endpoint\n    def generate(self, prompt: str, max_tokens: int = 128) -> str:\n        # サンプル: vLLMがOpenAI互換インターフェースを開いていると仮定\n        headers = {\"Content-Type\": \"application/json\"}\n        payload = {\n            \"model\": \"my-vllm-model\",\n            \"prompt\": prompt,\n            \"max_tokens\": max_tokens", "detail": "backend.inference.vllm_backend", "documentation": {}}, {"label": "XInferenceBackend", "kind": 6, "importPath": "backend.inference.x_inference_backend", "description": "backend.inference.x_inference_backend", "peekOfCode": "class XInferenceBackend(BaseLLMBackend):\n    def __init__(self, endpoint=\"http://x-inference:9999/v1\"):\n        self.endpoint = endpoint\n    def generate(self, prompt: str, max_tokens: int = 128) -> str:\n        headers = {\"Content-Type\": \"application/json\"}\n        payload = {\n            \"model\": \"x-inference-model\",\n            \"prompt\": prompt,\n            \"max_tokens\": max_tokens\n        }", "detail": "backend.inference.x_inference_backend", "documentation": {}}, {"label": "setup_logger", "kind": 2, "importPath": "backend.monitoring.logger", "description": "backend.monitoring.logger", "peekOfCode": "def setup_logger():\n    config_file = os.path.join(os.path.dirname(__file__), \"log_config.yaml\")\n    if os.path.exists(config_file):\n        with open(config_file, \"r\") as f:\n            config = yaml.safe_load(f.read())\n        logging.config.dictConfig(config)\n    else:\n        logging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger(\"myllm\")", "detail": "backend.monitoring.logger", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.monitoring.logger", "description": "backend.monitoring.logger", "peekOfCode": "logger = logging.getLogger(\"myllm\")", "detail": "backend.monitoring.logger", "documentation": {}}, {"label": "request_count", "kind": 5, "importPath": "backend.monitoring.metrics", "description": "backend.monitoring.metrics", "peekOfCode": "request_count = Counter('my_llm_request_count', '処理されたリクエストの数')", "detail": "backend.monitoring.metrics", "documentation": {}}, {"label": "load_documents_from_file", "kind": 2, "importPath": "backend.rag.document_loader", "description": "backend.rag.document_loader", "peekOfCode": "def load_documents_from_file(file_path: str):\n    loader = UnstructuredFileLoader(file_path)\n    return loader.load()", "detail": "backend.rag.document_loader", "documentation": {}}, {"label": "EmbeddingModel", "kind": 6, "importPath": "backend.rag.embeddings", "description": "backend.rag.embeddings", "peekOfCode": "class EmbeddingModel:\n    def __init__(self, model_name=\"hkunlp/instructor-large\"):\n        self.model = HuggingFaceEmbeddings(model_name=model_name)\n    def embed_text(self, text: str):\n        return self.model.embed_query(text)\n    def embed_documents(self, docs: list[str]):\n        return [self.model.embed_query(d) for d in docs]", "detail": "backend.rag.embeddings", "documentation": {}}, {"label": "RAGService", "kind": 6, "importPath": "backend.rag.rag_service", "description": "backend.rag.rag_service", "peekOfCode": "class RAGService:\n    def __init__(self, backend=\"vllm\"):\n        self.vector_store = VectorStore()\n        self.backend = get_backend(backend)\n    def run_rag(self, query: str):\n        results = self.vector_store.search(query, top_k=3)\n        relevant_texts = [r.payload[\"text\"] for r in results]\n        context = \"\\n\".join(relevant_texts)\n        prompt = f\"以下の情報に基づいて質問に答えてください:\\n{context}\\n\\n質問：{query}\\n回答：\"\n        return self.backend.generate(prompt)", "detail": "backend.rag.rag_service", "documentation": {}}, {"label": "VectorStore", "kind": 6, "importPath": "backend.rag.vector_store", "description": "backend.rag.vector_store", "peekOfCode": "class VectorStore:\n    def __init__(self, collection_name=\"my_collection\"):\n        self.client = QdrantClient(host=settings.QDRANT_HOST, port=settings.QDRANT_PORT)\n        self.collection_name = collection_name\n        self.embedding_model = EmbeddingModel()\n    def create_collection(self):\n        self.client.recreate_collection(\n            collection_name=self.collection_name,\n            vectors_config=qmodels.VectorParams(size=768, distance=\"Cosine\")\n        )", "detail": "backend.rag.vector_store", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.routers.admin", "description": "backend.routers.admin", "peekOfCode": "router = APIRouter()\************(\"/stats\")\nasync def get_stats(current_user = Depends(get_current_admin_user)):\n    # 統計情報の取得\n    pass\************(\"/system-status\")\nasync def get_system_status(current_user = Depends(get_current_admin_user)):\n    return {\n        \"cpu_usage\": psutil.cpu_percent(),\n        \"memory_usage\": psutil.virtual_memory().percent,", "detail": "backend.routers.admin", "documentation": {}}, {"label": "system_info", "kind": 2, "importPath": "backend.routers.admin_api", "description": "backend.routers.admin_api", "peekOfCode": "def system_info(current_user: dict = Depends(get_current_user)):\n    # ここでrolesをチェック可能\n    # if 'admin' not in current_user.get('roles', []):\n    #     raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=\"管理者権限がありません\")\n    return {\n        \"info\": \"これはシステムの管理者情報です\"\n    }", "detail": "backend.routers.admin_api", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.routers.admin_api", "description": "backend.routers.admin_api", "peekOfCode": "router = APIRouter(prefix=\"/admin\", tags=[\"Admin APIs\"])\************(\"/system-info\")\ndef system_info(current_user: dict = Depends(get_current_user)):\n    # ここでrolesをチェック可能\n    # if 'admin' not in current_user.get('roles', []):\n    #     raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=\"管理者権限がありません\")\n    return {\n        \"info\": \"これはシステムの管理者情報です\"\n    }", "detail": "backend.routers.admin_api", "documentation": {}}, {"label": "CompletionRequest", "kind": 6, "importPath": "backend.routers.proxy_api", "description": "backend.routers.proxy_api", "peekOfCode": "class CompletionRequest(BaseModel):\n    prompt: str\n    max_tokens: int = 128\n    backend: str = \"vllm\"\*************(\"/completions\")\ndef create_completion(req: CompletionRequest):\n    request_count.inc()\n    backend = get_backend(req.backend)\n    output = backend.generate(req.prompt, req.max_tokens)\n    return {\"completion\": output}", "detail": "backend.routers.proxy_api", "documentation": {}}, {"label": "create_completion", "kind": 2, "importPath": "backend.routers.proxy_api", "description": "backend.routers.proxy_api", "peekOfCode": "def create_completion(req: CompletionRequest):\n    request_count.inc()\n    backend = get_backend(req.backend)\n    output = backend.generate(req.prompt, req.max_tokens)\n    return {\"completion\": output}", "detail": "backend.routers.proxy_api", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.routers.proxy_api", "description": "backend.routers.proxy_api", "peekOfCode": "router = APIRouter(prefix=\"/proxy\", tags=[\"OpenAI-like Proxy\"])\nclass CompletionRequest(BaseModel):\n    prompt: str\n    max_tokens: int = 128\n    backend: str = \"vllm\"\*************(\"/completions\")\ndef create_completion(req: CompletionRequest):\n    request_count.inc()\n    backend = get_backend(req.backend)\n    output = backend.generate(req.prompt, req.max_tokens)", "detail": "backend.routers.proxy_api", "documentation": {}}, {"label": "get_profile", "kind": 2, "importPath": "backend.routers.user_api", "description": "backend.routers.user_api", "peekOfCode": "def get_profile(current_user: dict = Depends(get_current_user)):\n    return {\"user_info\": current_user}", "detail": "backend.routers.user_api", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.routers.user_api", "description": "backend.routers.user_api", "peekOfCode": "router = APIRouter(prefix=\"/users\", tags=[\"User APIs\"])\************(\"/me\")\ndef get_profile(current_user: dict = Depends(get_current_user)):\n    return {\"user_info\": current_user}", "detail": "backend.routers.user_api", "documentation": {}}, {"label": "celery_app", "kind": 5, "importPath": "backend.tasks.celery_app", "description": "backend.tasks.celery_app", "peekOfCode": "celery_app = Celery(\n    \"my_llm_tasks\",\n    broker=settings.RABBITMQ_URL,\n    backend=settings.REDIS_URL\n)\ncelery_app.conf.update(\n    task_serializer=\"json\",\n    result_serializer=\"json\",\n    accept_content=[\"json\"],\n    timezone=\"Asia/Shanghai\",", "detail": "backend.tasks.celery_app", "documentation": {}}, {"label": "rag_task", "kind": 2, "importPath": "backend.tasks.worker", "description": "backend.tasks.worker", "peekOfCode": "def rag_task(query: str):\n    service = RAGService()\n    return service.run_rag(query)", "detail": "backend.tasks.worker", "documentation": {}}, {"label": "Settings", "kind": 6, "importPath": "backend.config", "description": "backend.config", "peekOfCode": "class Settings(BaseSettings):\n    PROJECT_NAME: str = \"My LLM Platform\"\n    DEBUG: bool = True\n    KEYCLOAK_SERVER_URL: str = \"http://localhost:8080\"\n    KEYCLOAK_REALM: str = \"myrealm\"\n    KEYCLOAK_CLIENT_ID: str = \"myclient\"\n    KEYCLOAK_CLIENT_SECRET: str = \"mysecret\"\n    REDIS_URL: str = \"redis://localhost:6379\"\n    RABBITMQ_URL: str = \"amqp://user:password@localhost:5672//\"\n    # Qdrant", "detail": "backend.config", "documentation": {}}, {"label": "settings", "kind": 5, "importPath": "backend.config", "description": "backend.config", "peekOfCode": "settings = Settings()", "detail": "backend.config", "documentation": {}}, {"label": "User", "kind": 6, "importPath": "backend.database", "description": "backend.database", "peekOfCode": "class User(Base):\n    __tablename__ = \"users\"\n    id = Column(Integer, primary_key=True)\n    username = Column(String, unique=True)\n    hashed_password = Column(String)\n    role = Column(String)\n    conversations = relationship(\"Conversation\", back_populates=\"user\")\nclass Conversation(Base):\n    __tablename__ = \"conversations\"\n    id = Column(Integer, primary_key=True)", "detail": "backend.database", "documentation": {}}, {"label": "Conversation", "kind": 6, "importPath": "backend.database", "description": "backend.database", "peekOfCode": "class Conversation(Base):\n    __tablename__ = \"conversations\"\n    id = Column(Integer, primary_key=True)\n    user_id = Column(Integer, ForeignKey(\"users.id\"))\n    title = Column(String)\n    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))\n    user = relationship(\"User\", back_populates=\"conversations\")\n    messages = relationship(\"ChatMessage\", back_populates=\"conversation\")\nclass ChatMessage(Base):\n    __tablename__ = \"chat_messages\"", "detail": "backend.database", "documentation": {}}, {"label": "ChatMessage", "kind": 6, "importPath": "backend.database", "description": "backend.database", "peekOfCode": "class ChatMessage(Base):\n    __tablename__ = \"chat_messages\"\n    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))\n    role = Column(String(50), nullable=False)\n    content = Column(Text, nullable=False)    \n    conversation_id = Column(Integer, ForeignKey(\"conversations.id\"), nullable=False)    \n    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))\n    conversation = relationship(\"Conversation\", back_populates=\"messages\")\n    def __repr__(self):\n        return f\"<ChatMessage(id={self.id}, role={self.role}, conversation_id={self.conversation_id})>\"", "detail": "backend.database", "documentation": {}}, {"label": "SQLALCHEMY_DATABASE_URL", "kind": 5, "importPath": "backend.database", "description": "backend.database", "peekOfCode": "SQLALCHEMY_DATABASE_URL = \"sqlite+aiosqlite:///./chat_history.db\"\nengine = create_async_engine(SQLALCHEMY_DATABASE_URL, echo=True)\nAsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)\nBase = declarative_base()\nclass User(Base):\n    __tablename__ = \"users\"\n    id = Column(Integer, primary_key=True)\n    username = Column(String, unique=True)\n    hashed_password = Column(String)\n    role = Column(String)", "detail": "backend.database", "documentation": {}}, {"label": "engine", "kind": 5, "importPath": "backend.database", "description": "backend.database", "peekOfCode": "engine = create_async_engine(SQLALCHEMY_DATABASE_URL, echo=True)\nAsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)\nBase = declarative_base()\nclass User(Base):\n    __tablename__ = \"users\"\n    id = Column(Integer, primary_key=True)\n    username = Column(String, unique=True)\n    hashed_password = Column(String)\n    role = Column(String)\n    conversations = relationship(\"Conversation\", back_populates=\"user\")", "detail": "backend.database", "documentation": {}}, {"label": "AsyncSessionLocal", "kind": 5, "importPath": "backend.database", "description": "backend.database", "peekOfCode": "AsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)\nBase = declarative_base()\nclass User(Base):\n    __tablename__ = \"users\"\n    id = Column(Integer, primary_key=True)\n    username = Column(String, unique=True)\n    hashed_password = Column(String)\n    role = Column(String)\n    conversations = relationship(\"Conversation\", back_populates=\"user\")\nclass Conversation(Base):", "detail": "backend.database", "documentation": {}}, {"label": "Base", "kind": 5, "importPath": "backend.database", "description": "backend.database", "peekOfCode": "Base = declarative_base()\nclass User(Base):\n    __tablename__ = \"users\"\n    id = Column(Integer, primary_key=True)\n    username = Column(String, unique=True)\n    hashed_password = Column(String)\n    role = Column(String)\n    conversations = relationship(\"Conversation\", back_populates=\"user\")\nclass Conversation(Base):\n    __tablename__ = \"conversations\"", "detail": "backend.database", "documentation": {}}, {"label": "init_redis", "kind": 2, "importPath": "backend.dependencies", "description": "backend.dependencies", "peekOfCode": "def init_redis():\n    return redis.from_url(settings.REDIS_URL)", "detail": "backend.dependencies", "documentation": {}}, {"label": "Message", "kind": 6, "importPath": "backend.main", "description": "backend.main", "peekOfCode": "class Message(BaseModel):\n    role: str\n    content: str\nclass ChatRequest(BaseModel):\n    messages: List[Message]\n    model: Optional[str] = \"gpt-3.5-turbo\"\n# クライアントの初期化\nclient = AsyncOpenAI(api_key=os.getenv(\"OPENAI_API_KEY\"))\**********(\"/api/chat\")\nasync def chat(request: ChatRequest, db: Session = Depends(get_db)):", "detail": "backend.main", "documentation": {}}, {"label": "ChatRequest", "kind": 6, "importPath": "backend.main", "description": "backend.main", "peekOfCode": "class ChatRequest(BaseModel):\n    messages: List[Message]\n    model: Optional[str] = \"gpt-3.5-turbo\"\n# クライアントの初期化\nclient = AsyncOpenAI(api_key=os.getenv(\"OPENAI_API_KEY\"))\**********(\"/api/chat\")\nasync def chat(request: ChatRequest, db: Session = Depends(get_db)):\n    conversation_id = str(uuid.uuid4())\n    try:\n        # OpenAI APIの呼び出しを修正", "detail": "backend.main", "documentation": {}}, {"label": "read_root", "kind": 2, "importPath": "backend.main", "description": "backend.main", "peekOfCode": "def read_root():\n    return {\"message\": \"My LLM Platformからこんにちは！\"}\nif __name__ == \"__main__\":\n    uvicorn.run(\"main:app\", host=\"0.0.0.0\", port=8000, reload=settings.DEBUG)", "detail": "backend.main", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "backend.main", "description": "backend.main", "peekOfCode": "app = FastAPI(title=settings.PROJECT_NAME)\napp.include_router(proxy_api.router)\napp.include_router(user_api.router)\napp.include_router(admin_api.router)\nasync def startup_event():\n    # Redisを初期化\n    r = init_redis()\n    r.set(\"startup_check\", \"ok\")\n    # Qdrantインデックスを初期化 (サンプル)\n    rag_service = RAGService()", "detail": "backend.main", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "backend.main", "description": "backend.main", "peekOfCode": "client = AsyncOpenAI(api_key=os.getenv(\"OPENAI_API_KEY\"))\**********(\"/api/chat\")\nasync def chat(request: ChatRequest, db: Session = Depends(get_db)):\n    conversation_id = str(uuid.uuid4())\n    try:\n        # OpenAI APIの呼び出しを修正\n        response = await client.chat.completions.create(\n            model=request.model,\n            messages=[{\"role\": msg.role, \"content\": msg.content} for msg in request.messages],\n            stream=True", "detail": "backend.main", "documentation": {}}]